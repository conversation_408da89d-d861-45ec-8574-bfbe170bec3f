<template>
  <div class="publication-info-container">
    <!-- 刊物信息管理 - Page -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="普刊" name="general">
        <portal-table
          ref="generalTableRef"
          :showAddButton="false"
          :columns="generalColumns"
          :pagination="generalPagination"
          :search-items="generalSearchItems"
          :table-data="generalTableData"
          :loading="generalLoading"
          row-key="id"
          @search="handleGeneralSearch"
          @handle-size-change="handleGeneralSizeChange"
          @handle-current-change="handleGeneralCurrentChange"
          @handle-selection-change="handleGeneralSelectionChange"
        />
      </el-tab-pane>

      <el-tab-pane label="要情" name="important">
        <portal-table
          ref="importantTableRef"
          :showAddButton="false"
          :columns="importantColumns"
          :pagination="importantPagination"
          :search-items="importantSearchItems"
          :table-data="importantTableData"
          :loading="importantLoading"
          row-key="id"
          @search="handleImportantSearch"
          @handle-size-change="handleImportantSizeChange"
          @handle-current-change="handleImportantCurrentChange"
          @handle-selection-change="handleImportantSelectionChange"
        />
      </el-tab-pane>
    </el-tabs>

    <el-dialog
      title="预览"
      :visible.sync="showIframe"
      v-if="showIframe"
      width="80%"
      height="600px"
      :before-close="handleCloseIframe"
    >
      <iframe
        :src="preFileUrl"
        frameborder="0"
        width="100%"
        height="600px"
      ></iframe>
    </el-dialog>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import { dutyManagementApi } from "@/api";
import publicationApi from "@/api/publication";
import { getKKFilePreviewUrl } from "@/utils/publicMethod";
import { auth } from "@/utils";
import { conversionDateNotSecond } from "@/utils/publicMethod";

export default {
  name: "PublicationInfo",
  components: {
    PortalTable,
  },
  data() {
    return {
      showIframe: false,
      preFileUrl: "",

      activeTab: "general", // 默认选中普刊标签页

      // 普刊相关数据
      generalLoading: false,
      generalTableData: [],
      generalCurrentSearchParams: {},
      generalPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 要情相关数据
      importantLoading: false,
      importantTableData: [],
      importantCurrentSearchParams: {},
      importantPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 普刊搜索表单配置
      generalSearchItems: [
        {
          type: "input",
          prop: "periodicalTitle",
          label: "期号",
          width: 200,
          placeholder: "请输入期号",
        },
        {
          type: "startEndPicker",
          prop: "timeRange",
          label: "时间范围",
          placeholder: "请选择时间范围",
          width: 300,
        },
        {
          type: "select",
          prop: "status",
          label: "状态",
          placeholder: "请选择状态",
          options: [
            { label: "全部", value: "" },
            { label: "暂存", value: 0 },
            { label: "待批示", value: 1 },
            { label: "已批示", value: 2 },
          ],
          width: 200,
        },
      ],

      // 要情搜索表单配置
      importantSearchItems: [
        {
          type: "input",
          prop: "periodicalTitle",
          label: "期号",
          width: 200,
          placeholder: "请输入期号",
        },
        {
          type: "startEndPicker",
          prop: "timeRange",
          label: "时间范围",
          placeholder: "请选择时间范围",
          width: 300,
        },
        {
          type: "select",
          prop: "status",
          label: "状态",
          placeholder: "请选择状态",
          options: [
            { label: "全部", value: "" },
            { label: "暂存", value: 0 },
            { label: "待批示", value: 1 },
            { label: "已批示", value: 2 },
          ],
          width: 200,
        },
      ],

      // 普刊列表列配置
      generalColumns: [
        {
          prop: "periodicalTitle",
          label: "期号",
          text: true,
        },
        {
          prop: "submitTime",
          label: "提交时间",
          text: true,
          formatter: (row) => {
            return row.submitTime || "-";
          },
        },
        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "预览",
              permission: "general:preview",
              buttonClick: this.handleGeneralPreview,
              isShow: () => true,
            },
            {
              label: "编辑",
              permission: "general:edit",
              buttonClick: this.handleGeneralEdit,
              isShow: () => true,
            },
            {
              label: "上传附件",
              permission: "general:upload",
              buttonClick: this.handleGeneralUpload,
              isShow: () => true,
            },
            {
              label: "删除",
              permission: "general:delete",
              buttonClick: this.handleGeneralDelete,
              isShow: () => true,
            },
          ],
        },
      ],

      // 要情列表列配置
      importantColumns: [
        {
          prop: "periodicalTitle",
          label: "期号",
          text: true,
        },
        {
          prop: "submitTime",
          label: "提交时间",
          text: true,
          formatter: (row) => {
            return row.submitTime || "-";
          },
        },
        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "预览",
              permission: "important:preview",
              buttonClick: this.handleImportantPreview,
              isShow: () => true,
            },
            {
              label: "上传附件",
              permission: "important:upload",
              buttonClick: this.handleImportantUpload,
              isShow: () => true,
            },
          ],
        },
      ],
    };
  },
  mounted() {
    this.fetchGeneralData();
  },
  methods: {
    // Tab切换处理
    handleTabClick(tab) {
      if (tab.name === "general") {
        this.fetchGeneralData();
      } else if (tab.name === "important") {
        this.fetchImportantData();
      }
    },

    // 普刊相关方法
    async fetchGeneralData(searchParams = {}) {
      this.generalLoading = true;
      try {
        const params = {
          count: this.generalPagination.pageSize,
          page: this.generalPagination.currentPage,
          periodicalTitle: searchParams.periodicalTitle || "",
          startTime: searchParams.startTime || "",
          endTime: searchParams.endTime || "",
          status: searchParams.status !== undefined ? searchParams.status : "",
        };

        const response = await publicationApi.queryPublicationPage(params);
        const { code, data, message, error } = response;

        if (code !== 0) {
          console.error("普刊接口错误响应:", { code, message, error });
          this.$message.error(message || error || "普刊数据加载失败");
          this.generalTableData = [];
          this.generalPagination.total = 0;
          return;
        }

        this.generalTableData = this.processTableData(data.items || []);
        this.generalPagination.total = data.total || 0;
      } catch (error) {
        console.error("普刊数据加载失败:", error);
        this.$message.error("普刊数据加载失败");
        this.generalTableData = [];
        this.generalPagination.total = 0;
      } finally {
        this.generalLoading = false;
      }
    },

    handleGeneralSearch(searchData) {
      // 处理时间范围搜索
      if (searchData.timeRange && searchData.timeRange.length > 0) {
        searchData.startTime = conversionDateNotSecond(searchData.timeRange[0]);
        searchData.endTime = conversionDateNotSecond(searchData.timeRange[1]);
        delete searchData.timeRange;
      }

      this.generalCurrentSearchParams = searchData;
      this.generalPagination.currentPage = 1;
      this.fetchGeneralData(searchData);
    },

    handleGeneralSizeChange(size) {
      this.generalPagination.pageSize = size;
      this.fetchGeneralData(this.generalCurrentSearchParams);
    },

    handleGeneralCurrentChange(page) {
      this.generalPagination.currentPage = page;
      this.fetchGeneralData(this.generalCurrentSearchParams);
    },

    handleGeneralSelectionChange(selection) {
      // 处理普刊选择变化
    },

    handleGeneralPreview(row) {
      publicationApi.paginatedUrl().then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.handlePreview(data);
      });
    },

    handlePreview(url) {
      this.preFileUrl = getKKFilePreviewUrl(url);
      this.showIframe = true;
    },
    handlePreview(file) {
      let fileUrl = auth.getFileBaseUrl() + file;
      const fileExtension = fileUrl.split(".").pop().toLowerCase();
      const previewableExtensions = ["pdf", "jpg", "jpeg", "png", "gif"];
      if (!previewableExtensions.includes(fileExtension)) {
        // 如果文件类型不支持直接预览，则重新拼接URL
        fileUrl = getKKFilePreviewUrl(fileUrl);
      }
      this.preFileUrl = fileUrl;
      this.showIframe = true;
    },
    // 关闭预览框
    handleCloseIframe() {
      this.showIframe = false;
    },

    handleGeneralEdit(row) {
      // 跳转到编辑页面
      this.$router.push({
        path: `/informationSubmit/publicationInfo/publicationManagement/edit`,
        query: {
          id: row.id,
          type: "general",
        },
      });
    },

    handleGeneralUpload(row) {
      this.$message.info(`上传附件：${row.periodicalTitle}`);
    },

    handleGeneralDelete(row) {
      this.$confirm(
        `确定要删除期号为"${row.periodicalTitle}"的普刊吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.$message.success("删除成功");
        this.fetchGeneralData(this.generalCurrentSearchParams);
      });
    },

    // 要情相关方法
    async fetchImportantData(searchParams = {}) {
      this.importantLoading = true;
      try {
        const params = {
          count: this.importantPagination.pageSize,
          page: this.importantPagination.currentPage,
          periodicalTitle: searchParams.periodicalTitle || "",
          startTime: searchParams.startTime || "",
          endTime: searchParams.endTime || "",
          status: searchParams.status !== undefined ? searchParams.status : "",
        };

        const response = await publicationApi.queryPaginatedInfoPage(params);
        const { code, data, message, error } = response;

        if (code !== 0) {
          console.error("要情接口错误响应:", { code, message, error });
          this.$message.error(message || error || "要情数据加载失败");
          this.importantTableData = [];
          this.importantPagination.total = 0;
          return;
        }

        this.importantTableData = this.processTableData(data.items || []);
        this.importantPagination.total = data.total || 0;
      } catch (error) {
        console.error("要情数据加载失败:", error);
        this.importantTableData = [];
        this.importantPagination.total = 0;
      } finally {
        this.importantLoading = false;
      }
    },

    handleImportantSearch(searchData) {
      // 处理时间范围搜索
      if (searchData.timeRange && searchData.timeRange.length > 0) {
        searchData.startTime = conversionDateNotSecond(searchData.timeRange[0]);
        searchData.endTime = conversionDateNotSecond(searchData.timeRange[1]);
        delete searchData.timeRange;
      }

      this.importantCurrentSearchParams = searchData;
      this.importantPagination.currentPage = 1;
      this.fetchImportantData(searchData);
    },

    handleImportantSizeChange(size) {
      this.importantPagination.pageSize = size;
      this.fetchImportantData(this.importantCurrentSearchParams);
    },

    handleImportantCurrentChange(page) {
      this.importantPagination.currentPage = page;
      this.fetchImportantData(this.importantCurrentSearchParams);
    },

    handleImportantSelectionChange(selection) {
      // 处理要情选择变化
    },

    handleImportantPreview(row) {
      this.$message.info(`预览要情：${row.periodicalTitle}`);
    },

    handleImportantUpload(row) {
      this.$message.info(`上传附件：${row.periodicalTitle}`);
    },

    // 处理表格数据，转换状态值为显示文本
    processTableData(rawData) {
      const statusMap = {
        0: "暂存",
        1: "待批示",
        2: "已批示",
      };

      return rawData.map((item) => ({
        ...item,
        statusText: statusMap[item.status] || item.status || "",
        submitTime: item.submitTime || "-",
        approvalTime: item.approvalTime || "-",
      }));
    },
  },
};
</script>

<style scoped lang="scss">
.publication-info-container {
  padding: 20px;
}
</style>
